# Solar Icons Performance Configuration
# Copy these settings to your .env file for optimal performance

# Enable caching for better performance (recommended for production)
SOLAR_ICONS_CACHE_ENABLED=true

# Cache TTL in seconds (1 hour = 3600 seconds)
SOLAR_ICONS_CACHE_TTL=3600

# Enable lazy loading to improve application boot time
SOLAR_ICONS_LAZY_LOADING=true

# Only preload commonly used icon sets (comma-separated)
# Leave empty to disable preloading, or specify sets like: solar-outline,solar-linear
SOLAR_ICONS_PRELOAD_SETS=solar-outline,solar-linear

# Disable force rebuild in production for better performance
SOLAR_ICONS_FORCE_REBUILD=false

# Disable logging in production
SOLAR_ICONS_LOG_FLATTENING=false
SOLAR_ICONS_LOG_MISSING=false

# Laravel Performance Settings (recommended)
# Enable config caching: php artisan config:cache
# Enable route caching: php artisan route:cache
# Enable view caching: php artisan view:cache

# PHP Performance Settings (add to php.ini)
# opcache.enable=1
# opcache.memory_consumption=256
# opcache.max_accelerated_files=20000
# opcache.validate_timestamps=0 (production only)

# Memory Settings
# memory_limit=512M (adjust based on your needs)
